#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keno Predictor sử dụng Labeled Model
Tối ưu hóa cho tỷ lệ dự đoán ≥5/6 số đúng
"""

import joblib
import numpy as np
import glob
import os
from simple_keno_predictor import EnhancedKenoPredictor

class LabeledModelPredictor(EnhancedKenoPredictor):
    """Predictor sử dụng labeled model đã train"""

    def __init__(self, model_file=None, enable_short_term=True, enable_exclusion=True, enable_ensemble=True,
                 exclusion_threshold=3, exclusion_window=5, restore_after_periods=5):
        super().__init__(enable_short_term, enable_exclusion, enable_ensemble,
                        exclusion_threshold, exclusion_window, restore_after_periods)

        self.labeled_model = None
        self.model_scaler = None
        self.label_encoder = None
        self.model_info = None

        # Load model
        if model_file:
            self.load_labeled_model(model_file)
        else:
            self.load_latest_model()

    def get_latest_model(self):
        """Tìm model mới nhất"""
        pattern = "keno_labeled_model_v*.pkl"
        model_files = glob.glob(pattern)

        if not model_files:
            return None

        # Sắp xếp theo version
        def get_version(filename):
            try:
                return int(filename.split('_v')[1].split('.')[0])
            except:
                return 0

        latest_file = max(model_files, key=get_version)
        return latest_file

    def load_latest_model(self):
        """Load model mới nhất"""
        latest_model = self.get_latest_model()
        if latest_model:
            self.load_labeled_model(latest_model)
        else:
            print("⚠️ Không tìm thấy labeled model, sử dụng LSTM only")

    def load_labeled_model(self, model_file):
        """Load labeled model từ file"""
        try:
            model_package = joblib.load(model_file)

            self.labeled_model = model_package['model']
            self.model_scaler = model_package['scaler']
            self.label_encoder = model_package['label_encoder']
            self.model_info = {
                'model_type': model_package['model_type'],
                'performance': model_package['performance'],
                'created_at': model_package['created_at'],
                'file': model_file
            }

            print(f"✅ Loaded labeled model: {model_file}")
            print(f"   • Type: {self.model_info['model_type']}")
            print(f"   • ≥5/6 Recall: {self.model_info['performance']['good_vg_recall']:.3f}")
            print(f"   • Accuracy: {self.model_info['performance']['accuracy']:.3f}")

        except Exception as e:
            print(f"❌ Lỗi load model {model_file}: {e}")
            self.labeled_model = None

    def extract_current_features(self):
        """Trích xuất features từ trạng thái hiện tại"""
        if not self.enable_short_term or not self.data:
            return None

        # Phân tích xu hướng ngắn hạn
        short_trend = self.analyze_short_term_trend()
        if not short_trend:
            return None

        # Tạo feature vector giống như training
        feature_vector = []

        # Features từ number_stats (80 số x 6 features)
        for num in range(1, 81):
            if num in short_trend['number_stats']:
                stats = short_trend['number_stats'][num]
                feature_vector.extend([
                    stats['frequency_rate'],
                    stats['missing_rate'],
                    stats['gap_from_last'],
                    stats['momentum'],
                    stats['appearances'],
                    1 if stats['is_excluded'] else 0
                ])
            else:
                feature_vector.extend([0, 0, 0, 0, 0, 0])

        # Features bổ sung
        feature_vector.extend([
            len(self.data),  # Số kì đã có
            len(short_trend.get('excluded_numbers', set())),  # Số lượng số bị loại bỏ
            self.short_term_period,  # Chu kỳ phân tích
            1 if self.enable_exclusion else 0,  # Có bật exclusion
            1 if self.enable_ensemble else 0   # Có bật ensemble
        ])

        return np.array(feature_vector).reshape(1, -1)

    def predict_with_labeled_model(self, day_results=None, num_predictions=6):
        """Dự đoán sử dụng labeled model"""
        if not self.labeled_model or not self.enable_short_term:
            # Fallback về LSTM
            return self.predict_lstm_only(day_results, num_predictions)

        # Load dữ liệu nếu cần
        if not self.data:
            if not self.load_recent_data(100):
                return self.predict_lstm_only(day_results, num_predictions)

        # Trích xuất features
        features = self.extract_current_features()
        if features is None:
            return self.predict_lstm_only(day_results, num_predictions)

        try:
            # Scale features
            features_scaled = self.model_scaler.transform(features)

            # Predict label
            predicted_label_encoded = self.labeled_model.predict(features_scaled)[0]
            predicted_label = self.label_encoder.inverse_transform([predicted_label_encoded])[0]

            # Predict probabilities
            label_probs = self.labeled_model.predict_proba(features_scaled)[0]

            print(f"🎯 Labeled Model Prediction: {predicted_label}")

            # Hiển thị probabilities
            for i, prob in enumerate(label_probs):
                label = self.label_encoder.inverse_transform([i])[0]
                print(f"   • {label}: {prob:.3f}")

            # Dự đoán dựa trên label
            if predicted_label in ['Very Good', 'Good']:
                # Sử dụng ensemble prediction với trọng số cao hơn
                return self.predict_with_features(day_results, num_predictions)
            elif predicted_label == 'Normal':
                # Sử dụng LSTM + một ít features
                lstm_pred = self.predict_lstm_only(day_results, num_predictions)
                if self.enable_ensemble:
                    ensemble_pred = self.predict_with_features(day_results, num_predictions)
                    # Kết hợp 70% LSTM + 30% ensemble
                    combined = lstm_pred[:4] + ensemble_pred[4:6]
                    return combined[:num_predictions]
                return lstm_pred
            else:  # Bad
                # Chỉ dùng LSTM
                return self.predict_lstm_only(day_results, num_predictions)

        except Exception as e:
            print(f"❌ Lỗi labeled model prediction: {e}")
            return self.predict_lstm_only(day_results, num_predictions)

    def predict_missing_numbers(self, day_results=None, num_predictions=6, current_draw_index=None):
        """Override main prediction function để sử dụng labeled model"""
        if self.labeled_model and self.enable_short_term:
            return self.predict_with_labeled_model(day_results, num_predictions)
        else:
            # Fallback về parent method
            return super().predict_missing_numbers(day_results, num_predictions, current_draw_index)

    def get_model_info(self):
        """Lấy thông tin model"""
        if self.model_info:
            return self.model_info
        else:
            return {"status": "No labeled model loaded"}

def predict_with_labeled_model(model_file=None, exclusion_threshold=3, exclusion_window=5, restore_after_periods=5):
    """Dự đoán nhanh với labeled model"""
    predictor = LabeledModelPredictor(
        model_file=model_file,
        enable_short_term=True,
        enable_exclusion=True,
        enable_ensemble=True,
        exclusion_threshold=exclusion_threshold,
        exclusion_window=exclusion_window,
        restore_after_periods=restore_after_periods
    )

    predictions = predictor.predict_missing_numbers(num_predictions=6)

    if predictions:
        print(f"🎯 6 số dự đoán trượt (Labeled Model): {predictions}")
        return predictions
    else:
        print("❌ Không thể dự đoán")
        return []

def test_labeled_model(test_date=None, model_file=None, exclusion_threshold=3, exclusion_window=5, restore_after_periods=5):
    """Test labeled model với một ngày cụ thể"""
    predictor = LabeledModelPredictor(
        model_file=model_file,
        enable_short_term=True,
        enable_exclusion=True,
        enable_ensemble=True,
        exclusion_threshold=exclusion_threshold,
        exclusion_window=exclusion_window,
        restore_after_periods=restore_after_periods
    )

    if test_date is None:
        # Lấy ngày gần nhất
        test_dates = predictor.get_test_dates()
        if test_dates:
            test_date = test_dates[-1]
        else:
            print("❌ Không có ngày nào để test")
            return

    print(f"=== Test Labeled Model - Ngày {test_date} ===")

    # Hiển thị thông tin model
    model_info = predictor.get_model_info()
    if 'model_type' in model_info:
        print(f"Model: {model_info['model_type']} (≥5/6 Recall: {model_info['performance']['good_vg_recall']:.3f})")

    if predictor.enable_exclusion:
        print(f"Tính năng: Loại bỏ số xuất hiện quá {exclusion_threshold} lần trong {exclusion_window} kì gần nhất, đưa trở lại sau {restore_after_periods} kì")

    result = predictor.test_single_day(test_date)

    if result:
        print(f"\n📊 KẾT QUẢ:")
        print(f"   • Tỷ lệ ≥5/6: {result['perfect_predictions']}/{result['predictions_count']} ({result['perfect_rate']:.1f}%)")
        print(f"   • Vé thắng: {result.get('total_winning_tickets', 0)}/{result.get('total_tickets_played', 0)} ({result.get('overall_ticket_win_rate', 0):.1f}%)")

def test_all_days_labeled_model(model_file=None, exclusion_threshold=3, exclusion_window=5, restore_after_periods=5):
    """Test labeled model với tất cả ngày từ 2025-04-01"""
    predictor = LabeledModelPredictor(
        model_file=model_file,
        enable_short_term=True,
        enable_exclusion=True,
        enable_ensemble=True,
        exclusion_threshold=exclusion_threshold,
        exclusion_window=exclusion_window,
        restore_after_periods=restore_after_periods
    )

    print("=== Test Labeled Model - Tất cả ngày (từ 2025-04-01) ===")

    # Hiển thị thông tin model
    model_info = predictor.get_model_info()
    if 'model_type' in model_info:
        print(f"Model: {model_info['model_type']} (≥5/6 Recall: {model_info['performance']['good_vg_recall']:.3f})")

    print(f"Tính năng: Loại bỏ số xuất hiện quá {exclusion_threshold} lần trong {exclusion_window} kì gần nhất, đưa trở lại sau {restore_after_periods} kì")

    # Sử dụng test_all_days từ parent class
    predictor.test_all_days()

def main():
    """Main function"""
    import sys

    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()

        if mode == 'predict':
            # Dự đoán với labeled model
            model_file = sys.argv[2] if len(sys.argv) > 2 else None
            predict_with_labeled_model(model_file)

        elif mode == 'test':
            # Test labeled model một ngày
            test_date = sys.argv[2] if len(sys.argv) > 2 else None
            model_file = sys.argv[3] if len(sys.argv) > 3 else None
            test_labeled_model(test_date, model_file)

        elif mode == 'testall':
            # Test labeled model tất cả ngày
            model_file = sys.argv[2] if len(sys.argv) > 2 else None
            test_all_days_labeled_model(model_file)

        elif mode == 'info':
            # Hiển thị thông tin models
            pattern = "keno_labeled_model_v*.pkl"
            model_files = sorted(glob.glob(pattern))

            if not model_files:
                print("❌ Không tìm thấy labeled model nào")
                return

            print("📋 DANH SÁCH LABELED MODELS:")
            print("-" * 50)

            for model_file in model_files:
                try:
                    model_package = joblib.load(model_file)
                    perf = model_package['performance']
                    print(f"📁 {model_file}")
                    print(f"   • Type: {model_package['model_type']}")
                    print(f"   • ≥5/6 Recall: {perf['good_vg_recall']:.3f}")
                    print(f"   • Accuracy: {perf['accuracy']:.3f}")
                    print(f"   • Created: {model_package['created_at']}")
                    print()
                except Exception as e:
                    print(f"❌ Lỗi load {model_file}: {e}")
        else:
            print("Sử dụng:")
            print("  python labeled_model_predictor.py predict [model_file]     # Dự đoán")
            print("  python labeled_model_predictor.py test [date] [model_file] # Test một ngày")
            print("  python labeled_model_predictor.py testall [model_file]     # Test tất cả ngày")
            print("  python labeled_model_predictor.py info                     # Xem models")
            print()
            print("Cấu hình loại bỏ số (mặc định):")
            print("  - exclusion_threshold=3: Loại bỏ nếu xuất hiện >3 lần")
            print("  - exclusion_window=5: Trong 5 kì gần nhất")
            print("  - restore_after_periods=5: Đưa trở lại sau 5 kì")
    else:
        # Mặc định dự đoán
        predict_with_labeled_model()

if __name__ == "__main__":
    main()
