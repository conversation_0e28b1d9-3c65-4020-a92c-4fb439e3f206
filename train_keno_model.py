#!/usr/bin/env python3
"""
Training chính cho Variable Length Keno Model
"""

import tensorflow as tf
import os
from variable_length_model import VariableLengthKenoModel, connect_db, print_header, print_success, print_info, print_warning
import pandas as pd

# Thiết lập GPU
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

def setup_gpu():
    """Cấu hình GPU cho Apple Silicon"""
    print_header("CẤU HÌNH APPLE SILICON GPU")

    gpus = tf.config.list_physical_devices('GPU')
    if len(gpus) > 0:
        print_success(f"✅ Phát hiện Apple Silicon GPU: {len(gpus)} device(s)")
        print_info("🍎 MacBook Pro M4 Pro Specs:")
        print_info("   • CPU: 12-Core (8P + 4E)")
        print_info("   • GPU: 16-Core Apple GPU")
        print_info("   • RAM: 24GB Unified Memory")
        print_info("   • Memory Bandwidth: ~273 GB/s")

        try:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print_success("✅ Đã cấu hình GPU memory growth cho Apple Silicon")
            print_info("🚀 MPS (Metal Performance Shaders) backend activated")
        except Exception as e:
            print_warning(f"Cấu hình GPU: {e}")
    else:
        print_warning("⚠️ Không tìm thấy GPU, sử dụng CPU")

def load_real_data():
    """Tải dữ liệu thực từ database"""
    print_header("TẢI DỮ LIỆU THỰC")

    try:
        conn = connect_db()
        cursor = conn.cursor(dictionary=True)

        # Lấy dữ liệu trước ngày 2025-04-01 để tạo model chuẩn
        query = """
            SELECT date, time, results
            FROM histories_keno
            WHERE date < '2025-04-01'
            ORDER BY date DESC, time ASC
        """

        print_info("Đang truy vấn database (date < '2025-04-01')...")
        cursor.execute(query)
        rows = cursor.fetchall()

        cursor.close()
        conn.close()

        if len(rows) == 0:
            print_warning("Không có dữ liệu trong database trước ngày 2025-04-01")
            return None

        # Tạo DataFrame
        df = pd.DataFrame(rows)
        df['results'] = df['results'].apply(lambda x: [int(n) for n in x.split(',')])

        print_success(f"✅ Đã tải {len(df):,} records (date < '2025-04-01')")
        print_info(f"📅 Từ {df['date'].min()} đến {df['date'].max()}")
        print_info(f"🗓️ Tổng {df['date'].nunique()} ngày")

        return df

    except Exception as e:
        print_warning(f"Lỗi tải dữ liệu: {e}")
        return None

def analyze_data_quality(df):
    """Phân tích chất lượng dữ liệu"""
    print_header("PHÂN TÍCH CHẤT LƯỢNG DỮ LIỆU")

    # Phân tích theo ngày
    daily_counts = df.groupby('date').size()

    print_info(f"📊 THỐNG KÊ THEO NGÀY:")
    print(f"   • Tổng ngày: {len(daily_counts)}")
    print(f"   • Kỳ/ngày - Min: {daily_counts.min()}, Max: {daily_counts.max()}")
    print(f"   • Kỳ/ngày - Trung bình: {daily_counts.mean():.1f}")

    # Ngày có đủ 119 kỳ
    full_days = daily_counts[daily_counts >= 119]
    partial_days = daily_counts[daily_counts < 119]

    print_success(f"✅ Ngày đầy đủ (≥119 kỳ): {len(full_days)}")
    if len(partial_days) > 0:
        print_warning(f"⚠️ Ngày thiếu (<119 kỳ): {len(partial_days)}")

    # Ngày có thể train (≥50 kỳ)
    trainable_days = daily_counts[daily_counts >= 50]
    print_info(f"🎯 Ngày có thể train (≥50 kỳ): {len(trainable_days)}")

    return {
        'total_days': len(daily_counts),
        'full_days': len(full_days),
        'trainable_days': len(trainable_days),
        'daily_counts': daily_counts
    }

def train_production_model():
    """Train model production"""
    print_header("TRAINING PRODUCTION MODEL")

    # Setup GPU
    setup_gpu()

    # Tải dữ liệu
    df = load_real_data()
    if df is None:
        print_warning("Không thể tải dữ liệu, thoát")
        return None

    # Phân tích dữ liệu
    data_stats = analyze_data_quality(df)

    if data_stats['trainable_days'] < 10:
        print_warning("Không đủ dữ liệu để train (cần ít nhất 10 ngày)")
        return None

    # Khởi tạo model với sequence length tối ưu - GIỮ CHẤT LƯỢNG
    print_info("🚀 Khởi tạo Variable Length Model (Chất lượng cao)...")
    model = VariableLengthKenoModel(max_sequence_length=120)  # Giữ 120 để đảm bảo chất lượng
    print_info("📏 Max sequence length: 120 (đủ cho 119 kỳ/ngày + buffer)")

    # Tạo features
    X, y, lengths = model.create_variable_length_features(df)

    if len(X) == 0:
        print_warning("Không tạo được features")
        return None

    print_success(f"✅ Sẵn sàng train với {len(X):,} sequences")

    # Training tối ưu cho Apple Silicon M4 Pro
    epochs = 50  # Tăng từ 20->50 epochs để train đủ lâu
    batch_size = 1024  # Tăng lên 1024 để tận dụng 24GB RAM và 16-Core GPU
    print_info(f"🎯 Bắt đầu training với {epochs} epochs và batch size {batch_size}...")
    print_info("🍎 Tối ưu cho MacBook Pro M4 Pro: 16-Core GPU, 24GB Unified Memory")
    print_info("⚡ Target: 3-5 giờ training time (Apple Silicon optimized)")
    print_info("📊 Early stopping patience=4 để train đủ lâu")

    history = model.train_variable_model(X, y, lengths, epochs=epochs, batch_size=batch_size)

    if history:
        # Lưu model
        model_path = 'keno_variable_length_model.h5'
        model.model.save(model_path)
        print_success(f"✅ Đã lưu model: {model_path}")

        # Lưu thống kê
        import pickle
        stats = {
            'data_stats': data_stats,
            'model_config': {
                'max_sequence_length': model.max_sequence_length,
                'draws_per_day': model.draws_per_day,
                'total_sequences': len(X),
                'avg_sequence_length': float(lengths.mean()),
                'epochs_trained': epochs
            }
        }

        with open('model_stats.pkl', 'wb') as f:
            pickle.dump(stats, f)

        print_success("✅ Đã lưu thống kê model")

        # Đánh giá chất lượng training
        final_epoch = len(history.history['loss'])
        final_loss = history.history['loss'][-1]
        final_val_loss = history.history['val_loss'][-1]

        print_header("ĐÁNH GIÁ CHẤT LƯỢNG MODEL")
        print_info(f"📊 Dừng ở epoch: {final_epoch}/{epochs}")
        print_info(f"📉 Final loss: {final_loss:.4f}")
        print_info(f"📉 Final val_loss: {final_val_loss:.4f}")

        # Đánh giá thời gian training cho Apple Silicon
        estimated_time = final_epoch * 8  # ~8 phút/epoch với batch_size=1024 và Apple Silicon GPU
        print_info(f"⏱️ Thời gian training: ~{estimated_time//60}h {estimated_time%60}m (Apple Silicon optimized)")

        if final_epoch < 10:
            print_warning("⚠️ Model dừng quá sớm, có thể cần điều chỉnh early stopping")
        elif final_epoch > 25:
            print_success("✅ Model train đủ lâu, chất lượng rất tốt")
        else:
            print_success("✅ Model train hợp lý")

        if final_val_loss > final_loss * 1.2:
            print_warning("⚠️ Có dấu hiệu overfitting")
        else:
            print_success("✅ Không có overfitting")

        return model

    return None

def test_trained_model():
    """Test model đã train"""
    print_header("TEST MODEL ĐÃ TRAIN")

    try:
        # Load model
        model = VariableLengthKenoModel()
        model.model = tf.keras.models.load_model('keno_variable_length_model.h5')
        print_success("✅ Đã load model")

        # Test với dữ liệu mẫu
        print_info("🧪 Test với dữ liệu mẫu...")

        # Tạo ngày test với 119 kỳ
        test_day = []
        for i in range(119):
            numbers = list(range(1 + i % 12, 81, 3))[:20]
            test_day.append(numbers)

        # Test prediction ở các thời điểm
        test_points = [54, 79, 99, 118]  # Kỳ 55, 80, 100, 119

        for num_draws in test_points:
            input_data = test_day[:num_draws]
            target_draw = num_draws + 1

            print_info(f"\n📍 Dự đoán kỳ {target_draw} từ {len(input_data)} kỳ:")
            predictions = model.predict_missing_numbers(input_data, num_miss=6)

            if predictions:
                print_success(f"   ✅ 6 số trượt: {predictions}")

        return True

    except Exception as e:
        print_warning(f"Lỗi test model: {e}")
        return False

def main():
    """Main function"""
    print_header("KENO VARIABLE LENGTH MODEL TRAINING - MODEL CHUẨN")
    print_info("Logic: Dùng TẤT CẢ kỳ trước đó để dự đoán kỳ tiếp theo")
    print_info("Dữ liệu: Chỉ sử dụng date < '2025-04-01' để tạo model chuẩn")
    print()

    try:
        # Training
        model = train_production_model()

        if model:
            print_header("TRAINING THÀNH CÔNG")

            # Test model
            test_success = test_trained_model()

            if test_success:
                print_header("HOÀN THÀNH")
                print_success("✅ Model đã sẵn sàng sử dụng!")
                print_info("📁 Files được tạo:")
                print("   • keno_variable_length_model.h5 - Model đã train")
                print("   • model_stats.pkl - Thống kê training")
                print()
                print_info("🚀 Sử dụng:")
                print("   python predict_keno.py  # Để dự đoán")
            else:
                print_warning("⚠️ Model train thành công nhưng test có vấn đề")
        else:
            print_warning("❌ Training không thành công")

    except Exception as e:
        print_warning(f"Lỗi: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
