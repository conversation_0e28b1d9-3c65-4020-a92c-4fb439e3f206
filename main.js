const axios = require('axios');
const mysql = require('mysql2/promise');
const moment = require('moment');

// Cấu <PERSON>nh database
const dbConfig = {
    host: 'localhost',
    port: 3307,
    user: 'root',
    password: '1',
    database: 'keno'
};

// Hàm delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Hàm lấy dữ liệu từ API thực (dựa trên PHP code)
async function getData(day = 0, page = 0, sort = 'ASC') {
    try {
        const fromDate = moment().subtract(day, 'days').endOf('day').format('YYYY/MM/DD');
        const toDate = moment().subtract(day - 1, 'days').endOf('day').format('YYYY/MM/DD');

        const url = `https://api-knlt.gamingon.net/api/v1/rounds/1?limit=30&status=ENDED&sort=${sort}&page=${page}&from_date=${fromDate}&to_date=${toDate}`;

        console.log("URL: ", url);
        console.log(`🔍 Fetching data: day=${day}, page=${page}, from=${fromDate}, to=${toDate}`);

        const response = await axios.get(url, {
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            timeout: 15000
        });

        if (response.data && response.data.content) {
            return response.data.content;
        } else {
            console.log(`⚠️ Không có dữ liệu cho day=${day}, page=${page}`);
            return [];
        }
    } catch (error) {
        console.error(`❌ Lỗi lấy data day=${day}, page=${page}:`, error.message);
        return [];
    }
}

// Hàm lấy dữ liệu theo ngày (dựa trên getResultByDay từ PHP)
async function getResultByDay(day = 1, sort = 'ASC') {
    try {
        console.log(`🔍 Crawling data cho ngày cách đây ${day} ngày`);

        let allData = [];

        // Lấy data từ 4 pages (0, 1, 2, 3) như trong PHP
        for (let page = 3; page >= 0; page--) {
            const pageData = await getData(day, page, 'DESC');
            if (pageData && pageData.length > 0) {
                allData = allData.concat(pageData);
                console.log(`   📄 Page ${page}: ${pageData.length} records`);
            }

            // Delay giữa các page
            await delay(500);
        }

        // Sắp xếp theo thời gian để đảm bảo 06:00:00 đầu tiên, 06:08:00 tiếp theo...
        allData.sort((a, b) => {
            const timeA = moment(a.startAt);
            const timeB = moment(b.startAt);

            if (sort === 'ASC') {
                return timeA.isBefore(timeB) ? -1 : 1;
            } else {
                return timeA.isAfter(timeB) ? -1 : 1;
            }
        });

        console.log(`✅ Tổng cộng: ${allData.length} records cho ngày cách đây ${day} ngày`);
        console.log(`🕐 Thời gian đầu: ${moment(allData[0]?.startAt).format('HH:mm:ss')}`);
        console.log(`🕐 Thời gian cuối: ${moment(allData[allData.length-1]?.startAt).format('HH:mm:ss')}`);

        return allData;

    } catch (error) {
        console.error(`❌ Lỗi crawl ngày ${day}:`, error.message);
        return [];
    }
}

// Hàm lưu dữ liệu vào database (dựa trên importKenoData từ PHP)
async function saveToDatabase(data, targetDate = null) {
    if (!data || data.length === 0) {
        console.log(`⚠️ Không có dữ liệu để lưu`);
        return;
    }

    let connection;
    try {
        connection = await mysql.createConnection(dbConfig);

        let insertCount = 0;
        let skipCount = 0;

        // Nếu có targetDate, xóa tất cả data của ngày đó trước để đảm bảo thứ tự
        if (targetDate) {
            const [existingCount] = await connection.execute(
                'SELECT COUNT(*) as count FROM histories_keno WHERE date = ?',
                [targetDate]
            );

            if (existingCount[0].count > 0) {
                await connection.execute(
                    'DELETE FROM histories_keno WHERE date = ?',
                    [targetDate]
                );
                console.log(`🗑️ Đã xóa ${existingCount[0].count} records cũ của ngày ${targetDate}`);
            }
        }

        for (const item of data) {
            try {
                // Debug: Log raw startAt để kiểm tra format
                if (insertCount === 0) {
                    console.log(`🔍 Debug startAt format:`, typeof item.startAt, item.startAt);
                }

                // Parse thời gian từ startAt
                let startAt;
                if (typeof item.startAt === 'string') {
                    startAt = moment(item.startAt);
                } else if (item.startAt && item.startAt._isAMomentObject) {
                    // Nếu đã là moment object, dùng trực tiếp
                    startAt = item.startAt;
                } else {
                    // Fallback: convert về string
                    startAt = moment(String(item.startAt));
                }

                // Kiểm tra valid
                if (!startAt.isValid()) {
                    console.log(`⚠️ Invalid date for period ${item.periodId}:`, item.startAt);
                    continue;
                }

                const date = startAt.format('YYYY-MM-DD');
                const time = startAt.format('HH:mm:ss');

                // Nếu có targetDate, chỉ lưu data của ngày đó
                if (targetDate && date !== targetDate) {
                    skipCount++;
                    continue;
                }

                // Xử lý period: lấy periodId
                const period = item.periodId;

                // Xử lý result: chuyển "|" thành ","
                const results = item.resultRaw ? item.resultRaw.replace(/\|/g, ',') : '';

                if (!results) {
                    console.log(`⚠️ Không có kết quả cho period ${period}`);
                    continue;
                }

                // Tạo timestamps
                const now = moment().format('YYYY-MM-DD HH:mm:ss');

                // Chỉ insert (đã xóa data cũ ở trên)
                await connection.execute(
                    'INSERT INTO histories_keno (period, date, time, results, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)',
                    [period, date, time, results, now, now]
                );
                insertCount++;
            } catch (itemError) {
                console.error(`❌ Lỗi xử lý item:`, itemError.message);
                continue;
            }
        }

        console.log(`💾 Kết quả lưu: ${insertCount} records theo thứ tự thời gian, ${skipCount} bỏ qua`);

    } catch (error) {
        console.error(`❌ Lỗi lưu database:`, error.message);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// Hàm crawl nhiều ngày (dựa trên logic PHP)
async function crawlMultipleDays(fromDays, toDays = 1) {
    console.log(`🚀 Bắt đầu crawl từ ${fromDays} ngày trước đến ${toDays} ngày trước`);

    let totalRecords = 0;
    let processedDays = 0;
    const totalDays = fromDays - toDays + 1;

    for (let i = fromDays; i >= toDays; i--) {
        try {
            const targetDate = moment().subtract(i, 'days').format('YYYY-MM-DD');

            // Kiểm tra xem đã có dữ liệu chưa
            const connection = await mysql.createConnection(dbConfig);
            const [existing] = await connection.execute(
                'SELECT COUNT(*) as count FROM histories_keno WHERE date = ?',
                [targetDate]
            );
            await connection.end();

            if (existing[0].count > 0) {
                console.log(`⏭️ Ngày ${targetDate} đã có ${existing[0].count} records, bỏ qua`);
                processedDays++;
                continue;
            }

            console.log(`📅 Crawling ngày ${targetDate} (${i} ngày trước)`);

            // Lấy dữ liệu theo ngày với sort ASC để đảm bảo thứ tự thời gian
            const data = await getResultByDay(i, 'ASC');

            if (data && data.length > 0) {
                console.log(`📊 Insert ${data.length} records theo thứ tự: ${moment(data[0].startAt).format('HH:mm:ss')} → ${moment(data[data.length-1].startAt).format('HH:mm:ss')}`);
                await saveToDatabase(data, targetDate);
                totalRecords += data.length;
                console.log(`✅ Hoàn thành ngày ${targetDate}: ${data.length} records`);
            } else {
                console.log(`⚠️ Không có dữ liệu cho ngày ${targetDate}`);
            }

            processedDays++;
            console.log(`📊 Progress: ${processedDays}/${totalDays} ngày (${((processedDays/totalDays)*100).toFixed(1)}%)`);

            // Delay để tránh bị block (như trong PHP: sleep(6))
            await delay(6000);

        } catch (error) {
            console.error(`❌ Lỗi xử lý ngày ${i}:`, error.message);
            processedDays++;
        }
    }

    console.log(`🎉 Hoàn thành! Đã crawl ${processedDays} ngày, tổng ${totalRecords} records`);
}

// Hàm crawl ngày hôm nay với insert ngay khi có kỳ mới
async function crawlToday() {
    console.log(`📅 Crawl dữ liệu ngày hôm nay - Insert ngay khi có kỳ mới`);

    const today = moment().format('YYYY-MM-DD');
    const targetDraws = 119; // Mục tiêu 119 kỳ
    let attempts = 0;
    const maxAttempts = 120; // Tối đa 120 lần (2 giờ)
    let lastDrawCount = 0;

    while (attempts < maxAttempts) {
        attempts++;

        // Lấy data với sort ASC để đảm bảo 06:00:00 đầu tiên
        const data = await getResultByDay(0, 'ASC');

        if (data && data.length > 0) {
            console.log(`📊 Lần ${attempts}: Có ${data.length}/${targetDraws} kỳ`);

            // Nếu có kỳ mới, insert ngay
            if (data.length > lastDrawCount) {
                const newDraws = data.length - lastDrawCount;
                console.log(`🆕 Có ${newDraws} kỳ mới! Insert ngay vào DB...`);

                await saveToDatabase(data, today);
                lastDrawCount = data.length;

                console.log(`✅ Đã insert ${data.length} kỳ vào DB`);
            }

            // Kiểm tra đủ 119 kỳ chưa
            if (data.length >= targetDraws) {
                console.log(`🎉 Hoàn thành! Đã có đủ ${targetDraws} kỳ trong ngày`);
                break;
            } else {
                console.log(`⏳ Đợi 1 phút để kiểm tra kỳ tiếp theo...`);
                await delay(60000); // Đợi 1 phút
            }
        } else {
            console.log(`⚠️ Lần ${attempts}: Không có dữ liệu, thử lại sau 1 phút...`);
            await delay(60000);
        }
    }

    if (attempts >= maxAttempts) {
        console.log(`⚠️ Đã thử ${maxAttempts} lần. Kết thúc với ${lastDrawCount} kỳ`);
    }
}

// Hàm crawl ngày gần đây
async function crawlRecentDays(days = 7) {
    console.log(`📅 Crawl ${days} ngày gần đây`);
    await crawlMultipleDays(days, 1);
}

// Hàm kiểm tra dữ liệu thiếu
async function checkMissingData() {
    let connection;
    try {
        connection = await mysql.createConnection(dbConfig);

        // Lấy ngày đầu tiên và cuối cùng
        const [firstDate] = await connection.execute(
            'SELECT MIN(date) as first_date FROM histories_keno'
        );
        const [lastDate] = await connection.execute(
            'SELECT MAX(date) as last_date FROM histories_keno'
        );

        if (!firstDate[0].first_date || !lastDate[0].last_date) {
            console.log('⚠️ Chưa có dữ liệu trong database');
            return;
        }

        console.log(`📊 Kiểm tra dữ liệu từ ${firstDate[0].first_date} đến ${lastDate[0].last_date}`);

        // Kiểm tra các ngày thiếu
        const [missingDates] = await connection.execute(`
            SELECT date, COUNT(*) as count
            FROM histories_keno
            GROUP BY date
            HAVING count < 100
            ORDER BY date DESC
            LIMIT 10
        `);

        if (missingDates.length > 0) {
            console.log('⚠️ Các ngày có ít dữ liệu:');
            missingDates.forEach(row => {
                console.log(`   ${row.date}: ${row.count} records`);
            });
        } else {
            console.log('✅ Tất cả ngày đều có đủ dữ liệu');
        }

    } catch (error) {
        console.error('❌ Lỗi kiểm tra dữ liệu:', error.message);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// Main function
async function main() {
    const args = process.argv.slice(2);
    const command = args[0];

    switch (command) {
        case 'today':
            await crawlToday();
            break;

        case 'live':
            console.log('🔴 LIVE MODE: Crawl và insert ngay khi có kỳ mới');
            await crawlToday();
            break;

        case 'recent':
            const days = parseInt(args[1]) || 7;
            await crawlRecentDays(days);
            break;

        case 'range':
            if (args.length < 3) {
                console.log('❌ Sử dụng: node main.js range [fromDays] [toDays]');
                console.log('   Ví dụ: node main.js range 30 1  (crawl từ 30 ngày trước đến 1 ngày trước)');
                return;
            }
            const fromDays = parseInt(args[1]) || 30;
            const toDays = parseInt(args[2]) || 1;
            await crawlMultipleDays(fromDays, toDays);
            break;

        case 'check':
            await checkMissingData();
            break;

        default:
            console.log(`
🎯 Keno Data Crawler (API: api-knlt.gamingon.net)

Cách sử dụng:
  node main.js today                    - Crawl ngày hôm nay (insert ngay khi có kỳ mới)
  node main.js live                     - Live mode: Monitor và insert real-time
  node main.js recent [days]            - Crawl N ngày gần đây (mặc định 7)
  node main.js range [fromDays] [toDays] - Crawl từ X ngày trước đến Y ngày trước
  node main.js check                    - Kiểm tra dữ liệu thiếu

Ví dụ:
  node main.js today                    - Crawl hôm nay, insert ngay khi có kỳ mới
  node main.js live                     - Live monitoring (khuyến nghị)
  node main.js recent 10                - Crawl 10 ngày gần đây
  node main.js range 30 1               - Crawl từ 30 ngày trước đến 1 ngày trước
  node main.js range 80 1               - Crawl 80 ngày (như PHP code)
  node main.js check                    - Kiểm tra data thiếu

📝 Lưu ý:
  - API sử dụng: https://api-knlt.gamingon.net/api/v1/rounds/1
  - Delay 6 giây giữa các ngày (như PHP code)
  - Tự động bỏ qua ngày đã có dữ liệu
            `);
    }
}

// Chạy main function
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    getData,
    getResultByDay,
    saveToDatabase,
    crawlMultipleDays,
    crawlToday,
    crawlRecentDays,
    checkMissingData
};
