#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Train Keno Model với Labeled Dataset
Mục tiêu: T<PERSON><PERSON> ưu hóa tỷ lệ dự đoán ≥5/6 số đúng
"""

import json
import numpy as np
import os
import glob
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix
import joblib
import pandas as pd

class LabeledKenoModelTrainer:
    """Train model dựa trên labeled dataset để tối ưu tỷ lệ ≥5/6"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.label_encoder = LabelEncoder()
        self.feature_names = []
        
    def get_next_version(self, base_name="keno_labeled_model"):
        """Tìm version tiếp theo cho model"""
        pattern = f"{base_name}_v*.pkl"
        existing_files = glob.glob(pattern)
        
        if not existing_files:
            return f"{base_name}_v1.pkl"
        
        # Tìm version cao nhất
        versions = []
        for file in existing_files:
            try:
                version_str = file.split('_v')[1].split('.')[0]
                versions.append(int(version_str))
            except:
                continue
        
        next_version = max(versions) + 1 if versions else 1
        return f"{base_name}_v{next_version}.pkl"
    
    def load_labeled_dataset(self, filename="keno_labeled_dataset_full.json"):
        """Load labeled dataset từ file JSON"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"📁 Loaded dataset: {filename}")
            print(f"📊 Total entries: {data['metadata']['total_entries']}")
            print(f"📈 Label distribution: {data['metadata']['label_distribution']}")
            
            return data['data'], data['metadata']
            
        except FileNotFoundError:
            print(f"❌ File {filename} không tồn tại")
            return None, None
        except Exception as e:
            print(f"❌ Lỗi load dataset: {e}")
            return None, None
    
    def extract_features(self, dataset):
        """Trích xuất features từ dataset"""
        features = []
        labels = []
        
        for entry in dataset:
            # Features từ number_stats (80 số)
            number_stats = entry['features']['number_stats']
            feature_vector = []
            
            for num in range(1, 81):
                if str(num) in number_stats:
                    stats = number_stats[str(num)]
                    feature_vector.extend([
                        stats['frequency_rate'],
                        stats['missing_rate'],
                        stats['gap_from_last'],
                        stats['momentum'],
                        stats['appearances'],
                        1 if stats['is_excluded'] else 0  # Số bị loại bỏ
                    ])
                else:
                    feature_vector.extend([0, 0, 0, 0, 0, 0])
            
            # Features bổ sung
            feature_vector.extend([
                entry['day_results_count'],  # Số kì đã có trong ngày
                len(entry['features']['excluded_numbers']),  # Số lượng số bị loại bỏ
                entry['features']['short_term_period'],  # Chu kỳ phân tích
                1 if entry['enable_exclusion'] else 0,  # Có bật exclusion không
                1 if entry['enable_ensemble'] else 0   # Có bật ensemble không
            ])
            
            features.append(feature_vector)
            labels.append(entry['label'])
        
        return np.array(features), np.array(labels)
    
    def prepare_data(self, features, labels):
        """Chuẩn bị dữ liệu cho training"""
        # Encode labels
        labels_encoded = self.label_encoder.fit_transform(labels)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            features, labels_encoded, test_size=0.2, random_state=42, stratify=labels_encoded
        )
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        self.scalers['main'] = scaler
        
        print(f"📊 Training set: {X_train.shape[0]} samples")
        print(f"📊 Test set: {X_test.shape[0]} samples")
        print(f"📊 Features: {X_train.shape[1]} dimensions")
        
        return X_train_scaled, X_test_scaled, y_train, y_test
    
    def train_models(self, X_train, y_train):
        """Train multiple models"""
        print("\n🔄 Training models...")
        
        # Random Forest - Tốt cho feature importance
        print("   • Training Random Forest...")
        rf = RandomForestClassifier(
            n_estimators=200,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )
        rf.fit(X_train, y_train)
        self.models['random_forest'] = rf
        
        # Gradient Boosting - Tốt cho accuracy
        print("   • Training Gradient Boosting...")
        gb = GradientBoostingClassifier(
            n_estimators=200,
            learning_rate=0.1,
            max_depth=8,
            random_state=42
        )
        gb.fit(X_train, y_train)
        self.models['gradient_boosting'] = gb
        
        # Logistic Regression - Baseline
        print("   • Training Logistic Regression...")
        lr = LogisticRegression(
            random_state=42,
            max_iter=1000,
            multi_class='ovr'
        )
        lr.fit(X_train, y_train)
        self.models['logistic_regression'] = lr
        
        print("✅ Training completed!")
    
    def evaluate_models(self, X_test, y_test):
        """Đánh giá models"""
        print("\n📊 ĐÁNH GIÁ MODELS:")
        print("="*60)
        
        results = {}
        
        for name, model in self.models.items():
            print(f"\n🔍 {name.upper()}:")
            
            # Predictions
            y_pred = model.predict(X_test)
            
            # Classification report
            report = classification_report(y_test, y_pred, 
                                         target_names=self.label_encoder.classes_,
                                         output_dict=True)
            
            # Tính tỷ lệ Good + Very Good (≥5/6)
            good_very_good_indices = []
            for i, label in enumerate(self.label_encoder.classes_):
                if label in ['Good', 'Very Good']:
                    good_very_good_indices.append(i)
            
            # Tính precision/recall cho ≥5/6
            good_vg_precision = np.mean([report[self.label_encoder.classes_[i]]['precision'] 
                                       for i in good_very_good_indices])
            good_vg_recall = np.mean([report[self.label_encoder.classes_[i]]['recall'] 
                                    for i in good_very_good_indices])
            
            accuracy = report['accuracy']
            
            print(f"   • Overall Accuracy: {accuracy:.3f}")
            print(f"   • ≥5/6 Precision: {good_vg_precision:.3f}")
            print(f"   • ≥5/6 Recall: {good_vg_recall:.3f}")
            
            # Confusion matrix
            cm = confusion_matrix(y_test, y_pred)
            print(f"   • Confusion Matrix:")
            print(f"     {self.label_encoder.classes_}")
            for i, row in enumerate(cm):
                print(f"     {self.label_encoder.classes_[i]}: {row}")
            
            results[name] = {
                'accuracy': accuracy,
                'good_vg_precision': good_vg_precision,
                'good_vg_recall': good_vg_recall,
                'report': report
            }
        
        return results
    
    def save_best_model(self, results):
        """Lưu model tốt nhất"""
        # Chọn model tốt nhất dựa trên ≥5/6 recall
        best_model_name = max(results.keys(), 
                            key=lambda x: results[x]['good_vg_recall'])
        
        best_model = self.models[best_model_name]
        best_scaler = self.scalers['main']
        
        # Tạo version mới
        model_filename = self.get_next_version("keno_labeled_model")
        
        # Lưu model package
        model_package = {
            'model': best_model,
            'scaler': best_scaler,
            'label_encoder': self.label_encoder,
            'model_type': best_model_name,
            'performance': results[best_model_name],
            'created_at': datetime.now().isoformat(),
            'feature_count': len(self.feature_names) if self.feature_names else 'unknown'
        }
        
        joblib.dump(model_package, model_filename)
        
        print(f"\n💾 Đã lưu model tốt nhất: {model_filename}")
        print(f"   • Model type: {best_model_name}")
        print(f"   • ≥5/6 Recall: {results[best_model_name]['good_vg_recall']:.3f}")
        print(f"   • Overall Accuracy: {results[best_model_name]['accuracy']:.3f}")
        
        return model_filename
    
    def train_from_dataset(self, dataset_file="keno_labeled_dataset_full.json"):
        """Main training pipeline"""
        print("🚀 BẮT ĐẦU TRAINING LABELED MODEL")
        print("="*50)
        
        # 1. Load dataset
        dataset, metadata = self.load_labeled_dataset(dataset_file)
        if dataset is None:
            return None
        
        # 2. Extract features
        print("\n🔧 Extracting features...")
        features, labels = self.extract_features(dataset)
        
        # 3. Prepare data
        print("\n📋 Preparing data...")
        X_train, X_test, y_train, y_test = self.prepare_data(features, labels)
        
        # 4. Train models
        self.train_models(X_train, y_train)
        
        # 5. Evaluate models
        results = self.evaluate_models(X_test, y_test)
        
        # 6. Save best model
        model_filename = self.save_best_model(results)
        
        print(f"\n✅ TRAINING HOÀN THÀNH!")
        print(f"📁 Model đã lưu: {model_filename}")
        
        return model_filename

def main():
    """Main function"""
    import sys
    
    trainer = LabeledKenoModelTrainer()
    
    if len(sys.argv) > 1:
        dataset_file = sys.argv[1]
    else:
        dataset_file = "keno_labeled_dataset_full.json"
    
    # Kiểm tra file tồn tại
    if not os.path.exists(dataset_file):
        print(f"❌ File {dataset_file} không tồn tại!")
        print("💡 Hãy chạy: python simple_keno_predictor.py dataset")
        return
    
    # Train model
    model_file = trainer.train_from_dataset(dataset_file)
    
    if model_file:
        print(f"\n🎯 Sử dụng model: {model_file}")
        print("💡 Tích hợp vào predictor để test hiệu suất!")

if __name__ == "__main__":
    main()
