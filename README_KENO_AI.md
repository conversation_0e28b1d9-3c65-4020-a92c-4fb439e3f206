# 🤖 KENO AI PREDICTOR v2.0

## 🎯 Advanced Deep Historical Analysis System

Hệ thống dự đoán <PERSON>o AI tiên tiến với phân tích sâu 5,000 kì lịch sử, đạt tỷ lệ chính xác 31.72% cho dự đoán 4/4 số đúng.

---

## 📊 THỐNG KÊ HIỆU SUẤT

| Chỉ số | Giá trị | Ghi chú |
|--------|---------|---------|
| **Tỷ lệ 4/4 đúng** | **31.72%** | Đã test trên 9,820 predictions |
| **Thời gian test** | 143 ngày | Từ 2025-01-04 đến 2025-05-27 |
| **C<PERSON><PERSON> thiện so với LSTM** | **1,057x** | Từ 0.03% lên 31.72% |
| **Dữ liệu phân tích** | 5,000 kì | Deep Historical Analysis |
| **ROI thực tế** | 1.51% | Với tỷ lệ thưởng 32k/10k |

---

## 🚀 CÁCH SỬ DỤNG

### 1. Chạy Interactive Menu
```bash
python keno_ai_predictor.py
```

### 2. Command Line (Nhanh)
```bash
# Dự đoán ngay
python keno_ai_predictor.py predict

# Test độ chính xác (5 ngày)
python keno_ai_predictor.py test 5

# Mô phỏng betting (100k vốn, 3 ngày)
python keno_ai_predictor.py simulate 100000 3

# Xuất predictions ra file
python keno_ai_predictor.py export

# Xem thống kê nhanh
python keno_ai_predictor.py stats

# Phân tích khung giờ tốt nhất
python keno_ai_predictor.py times
```

### 3. Help
```bash
python keno_ai_predictor.py --help
```

---

## 🔧 TÍNH NĂNG CHÍNH

### 🧠 Deep Historical Analysis
- **5,000 periods** dữ liệu lịch sử
- **Long-term patterns** phân tích chu kỳ dài
- **Seasonal analysis** theo giờ, ngày, tháng
- **Pattern depth** 1,000 kì

### 🎯 Smart Prediction
- **5 số ứng viên** từ AI analysis
- **1 bộ 4 số tối ưu** cho betting
- **Consecutive exclusion** loại bỏ số xuất hiện liên tục
- **Recovery mechanism** tự động đưa số trở lại

### 💰 Martingale Strategy
- **Smart risk management** LOW/MEDIUM/HIGH
- **Progressive betting** với hệ số 2.2x
- **Stop loss** tự động tại 1M VNĐ
- **7 levels** tối đa

### 📊 Testing & Analysis
- **Accuracy testing** trên nhiều ngày
- **Betting simulation** với vốn thực
- **Time optimization** tìm khung giờ tốt nhất
- **Export predictions** ra file

---

## 📋 YÊU CẦU HỆ THỐNG

### Dependencies
```bash
pip install mysql-connector-python
```

### Database
- MySQL server với bảng `histories_keno`
- Cấu hình kết nối trong `db_config.py`

### Python
- Python 3.7+
- Modules: sys, os, datetime, mysql.connector, itertools

---

## 🎮 HƯỚNG DẪN SỬ DỤNG CHI TIẾT

### 1. Dự đoán Real-time
```python
# Chọn option 1 trong menu
# Hoặc: python keno_ai_predictor.py predict
```
**Kết quả:**
- 5 số ứng viên AI
- 1 bộ 4 số tối ưu để đánh
- Khuyến nghị Martingale (số tiền, risk level)

### 2. Test Độ Chính Xác
```python
# Chọn option 2, nhập số ngày test
# Hoặc: python keno_ai_predictor.py test 10
```
**Kết quả:**
- Tỷ lệ 4/4 đúng trên từng ngày
- Tỷ lệ tổng thể
- So sánh với expected 31.72%

### 3. Mô Phỏng Betting
```python
# Chọn option 3, nhập vốn ban đầu và số ngày
# Hoặc: python keno_ai_predictor.py simulate 200000 5
```
**Kết quả:**
- Lãi/lỗ theo từng kì
- ROI cuối cùng
- Thống kê win rate

### 4. Export Predictions
```python
# Chọn option 5, nhập tên file (optional)
# Hoặc: python keno_ai_predictor.py export my_predictions.txt
```
**Kết quả:**
- File text với 5 predictions
- Bao gồm timestamp, candidates, best combo
- Hướng dẫn sử dụng

---

## ⚠️ LƯU Ý QUAN TRỌNG

### 🎯 Về Độ Chính Xác
- **31.72%** là tỷ lệ trung bình qua 143 ngày
- Mỗi ngày có thể khác nhau (0-80%)
- Không đảm bảo kết quả trong tương lai

### 💰 Về Lợi Nhuận
- **ROI 1.51%** với tỷ lệ thưởng 32k/10k
- Cần vốn lớn để có lãi đáng kể
- **68.28%** tỷ lệ thua mỗi lần đánh

### 🎲 Về Rủi Ro
- Keno là trò chơi may rủi
- Luôn có khả năng mất tiền
- Chỉ đánh với số tiền có thể mất được

### 📚 Mục Đích
- **Giáo dục** và **giải trí**
- **Nghiên cứu** thuật toán AI
- **Không khuyến khích** cờ bạc

---

## 🔍 THUẬT TOÁN

### Statistical Approach
1. **Missing Numbers Analysis** - Phân tích số trượt
2. **Frequency Analysis** - Tần suất xuất hiện
3. **Pattern Recognition** - Nhận dạng pattern
4. **Long-term Trends** - Xu hướng dài hạn
5. **Seasonal Patterns** - Pattern theo thời gian
6. **Consecutive Exclusion** - Loại bỏ thông minh

### Scoring System
- **25%** Recent frequency
- **25%** Recent trends  
- **20%** Short-term patterns
- **30%** Long-term historical patterns

---

## 📞 HỖ TRỢ

### Lỗi Thường Gặp
1. **Database connection error**
   - Kiểm tra MySQL server
   - Xem lại cấu hình `db_config.py`

2. **Insufficient data**
   - Cần ít nhất 500 kì trong database
   - Kiểm tra bảng `histories_keno`

3. **Import error**
   - Đảm bảo file `simplified_keno_predictor.py` cùng thư mục
   - Cài đặt dependencies

### Debug Mode
```bash
# Chạy với verbose output
python simplified_keno_predictor.py predict
```

---

## 📈 ROADMAP

### v2.1 (Planned)
- [ ] Web interface
- [ ] Real-time notifications
- [ ] Advanced risk management
- [ ] Multiple betting strategies

### v2.2 (Future)
- [ ] Machine learning integration
- [ ] Multi-game support
- [ ] Cloud deployment
- [ ] Mobile app

---

## 📄 LICENSE

Educational and research purposes only. Use at your own risk.

**⚠️ DISCLAIMER: This software is for educational purposes only. Gambling involves risk of loss. The authors are not responsible for any financial losses.**

---

*Keno AI Predictor v2.0 - Advanced Deep Historical Analysis System*
*© 2025 Keno AI Team*
